## Follow this :

.\.venv\Scripts\activate

use uv pip install fastmcp

sample :
deno run \
  -N -R=node_modules -W=node_modules --node-modules-dir=auto \
  jsr:@pydantic/mcp-run-python [stdio|sse|warmup]

  
deno run -N -R=node_modules -W=node_modules --node-modules-dir=auto  jsr:@pydantic/mcp-run-python sse 3001

python fastmcp_tool_example.py

sample_fastmcp.py - 


## Instal scqenctail thing
git clone https://github.com/modelcontextprotocol/servers.git
cd C:\JK\dev\repo\scq_thinking_server\servers
docker build -f src/sequentialthinking/Dockerfile -t sequentialthinking .