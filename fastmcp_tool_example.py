from pydantic import BaseModel, Field
from pydantic_ai import Agent, RunContext
from pydantic_ai.mcp import MCPServerHTTP, MCPServerStdio
from openai import AsyncAzureOpenAI
from pydantic_ai.models.openai import OpenAIModel, OpenAIModelSettings
from pydantic_ai.providers.openai import OpenAIProvider
from pydantic_ai.agent import InstrumentationSettings
from typing import List, Dict, Any, Optional
import asyncio
import datetime
import logfire
import os
import requests

# Configure logfire with project token
logfire.configure(token='pylf_v1_us_lXJfnzpClJGNPv0J4lWQLqRstwMNYqM9d4kcS5tYTF0J')

# Instrument relevant components
logfire.instrument_pydantic_ai()
logfire.instrument_httpx(capture_all=True)
logfire.instrument_mcp()

logfire.info('Starting fastmcp with tool example and Context7 integration')

# Create Azure OpenAI client
endpoint = "https://pep-ee-pepgenxsbx-nonprod-eus2-openai.openai.azure.com"
azure_client = AsyncAzureOpenAI(
    azure_endpoint=endpoint,
    api_version="2025-01-01-preview",
    api_key="********************************"
)

# Create OpenAI model with Azure provider
model = OpenAIModel(
    'gpt-4.1',
    provider=OpenAIProvider(openai_client=azure_client)
)

# Create model settings
model_settings = OpenAIModelSettings(openai_user="test_user")

# Create instrumentation settings for better logfire integration
instrumentation_settings = InstrumentationSettings(
    event_mode='logs',  # Use logs event mode for better logfire integration
)

# Set up the MCP server (python execution server)
python_exec_server = MCPServerHTTP(url='http://localhost:3001/sse')

# Define our output model
class DeveloperAssistantResponse(BaseModel):
    answer: str = Field(description="The answer to the user's question")
    code_snippets: Optional[List[str]] = Field(
        default=None, 
        description="Code snippets if relevant"
    )
    references: Optional[List[str]] = Field(
        default=None, 
        description="Documentation references used"
    )


# Create Context7 server if Deno is available, otherwise set to None
try:
    # Try multiple ways to find deno
    import shutil
    import subprocess
    import sys
    
    # Method 1: Try to find deno using shutil.which
    deno_path = shutil.which('deno')
    
    # Method 2: If not found, try to execute deno directly to see if it works
    if not deno_path:
        try:
            # On Windows, we need shell=True to find commands in PATH
            result = subprocess.run(['deno', '--version'], 
                                   stdout=subprocess.PIPE, 
                                   stderr=subprocess.PIPE,
                                   shell=True,
                                   check=True,
                                   text=True)
            # If we get here, deno exists and is in PATH
            logfire.info(f'Deno found via subprocess check: {result.stdout.strip()}')
            deno_path = 'deno'  # Just use the command name since it's in PATH
        except (subprocess.SubprocessError, FileNotFoundError) as e:
            logfire.warning(f'Deno subprocess check failed: {str(e)}')
    
    # Debug info about environment
    logfire.info(f'PATH environment: {os.environ.get("PATH", "PATH not found")}')
    logfire.info(f'Running Python from: {sys.executable}')
    
    if deno_path:
        logfire.info(f'Deno found at: {deno_path}')
        context7 = MCPServerStdio(
            command=deno_path,
            args=[
                "run",
                "-A",  # Allow all permissions for simplicity
                "npm:@upstash/context7-mcp@latest",  # Ensure using latest version
                "stdio",
            ],
            env=os.environ,  # inherit your environment
        )
    else:
        logfire.warning('Deno not found after multiple detection methods, Context7 server will be disabled')
        context7 = None
except Exception as e:
    logfire.error(f'Error setting up Context7: {str(e)}')
    context7 = None

# Set up the Sequential Thinking MCP server using Docker
try:
    # Try to find docker using the same pattern as Deno
    docker_path = shutil.which('docker')

    # If not found, try to execute docker directly to see if it works
    if not docker_path:
        try:
            # On Windows, we need shell=True to find commands in PATH
            result = subprocess.run(['docker', '--version'],
                                   stdout=subprocess.PIPE,
                                   stderr=subprocess.PIPE,
                                   shell=True,
                                   check=True,
                                   text=True)
            # If we get here, docker exists and is in PATH
            logfire.info(f'Docker found via subprocess check: {result.stdout.strip()}')
            docker_path = 'docker'  # Just use the command name since it's in PATH
        except (subprocess.SubprocessError, FileNotFoundError) as e:
            logfire.warning(f'Docker subprocess check failed: {str(e)}')

    if docker_path:
        logfire.info(f'Docker found at: {docker_path}')
        sequential_thinking_server = MCPServerStdio(
            command=docker_path,
            args=[
                "run",
                "--rm",
                "-i",
                "sequentialthinking"
            ],
            env=os.environ,  # inherit your environment
        )
    else:
        logfire.warning('Docker not found after multiple detection methods, Sequential Thinking server will be disabled')
        sequential_thinking_server = None
except Exception as e:
    logfire.error(f'Error setting up Sequential Thinking server: {str(e)}')
    sequential_thinking_server = None

# Create the agent with tool capabilities
# Add MCP servers only if available
mcp_servers = [python_exec_server]
if context7:
    mcp_servers.append(context7)
if sequential_thinking_server:
    mcp_servers.append(sequential_thinking_server)

# Create system prompt based on available capabilities
capabilities = []
if context7:
    capabilities.append("Context7 to retrieve up-to-date technical documentation")
if sequential_thinking_server:
    capabilities.append("sequential thinking tools for complex problem-solving and analysis")

if capabilities:
    system_prompt = (
        f"You are a Python development assistant with access to external tools "
        f"and {', '.join(capabilities)}. Always provide accurate, well-documented "
        f"code examples based on the latest information."
    )
else:
    system_prompt = (
        "You are a Python development assistant with access to external tools. "
        "Always provide accurate, well-documented code examples."
    )

fastmcp_agent = Agent(
    model,
    output_type=DeveloperAssistantResponse,
    mcp_servers=mcp_servers,
    model_settings=model_settings,
    instrument=instrumentation_settings,  # Enable instrumentation
    system_prompt=system_prompt,
)

# Tool for getting the current time
@fastmcp_agent.tool()
def get_current_time(ctx: RunContext) -> Dict[str, Any]:
    """Get the current date and time"""
    with logfire.span('Getting current time'):
        now = datetime.datetime.now()
        logfire.info(f'Current time requested: {now.isoformat()}')
        return {
            "iso_format": now.isoformat(),
            "formatted": now.strftime("%Y-%m-%d %H:%M:%S"),
            "timestamp": now.timestamp()
        }

# Tool for calculating factorial
@fastmcp_agent.tool()
def calculate_factorial(ctx: RunContext, n: int) -> Dict[str, Any]:
    """Calculate the factorial of a number n"""
    with logfire.span(f'Calculating factorial of {n}'):
        if n < 0:
            logfire.error(f'Factorial calculation error: negative input {n}')
            return {"error": "Factorial is not defined for negative numbers"}
        
        result = 1
        for i in range(1, n + 1):
            result *= i
        
        logfire.info(f'Factorial calculated: {n}! = {result}')
        return {
            "input": n,
            "result": result
        }

# Tool for getting city weather data
@fastmcp_agent.tool()
def get_city_weather(ctx: RunContext, city: str, data_type: str = "all") -> Dict[str, Any]:
    """Get temperature and humidity data for a city
    
    Parameters:
    city (str): Name of the city to get weather data for
    data_type (str): Type of data to return - 'temperature', 'humidity', or 'all' (default)
    """
    with logfire.span(f'Getting weather data for {city}'):
        try:
            # Using the OpenWeatherMap API
            api_key = "********************************"
            base_url = "https://api.openweathermap.org/data/2.5/weather"
            params = {
                "q": city,
                "appid": api_key,
                "units": "metric"  # for Celsius
            }
            
            # Making API request
            logfire.info(f'Making OpenWeatherMap API request for {city}')
            
            # Make the API request
            response = requests.get(base_url, params=params, timeout=5)
            
            # Check status code before proceeding
            if response.status_code != 200:
                error_info = f"API returned status code: {response.status_code} - {response.text}"
                logfire.warning(f"Weather API error: {error_info}")
                return {"error": f"Could not retrieve weather data for {city}: {error_info}"}
                
            # Parse the API response
            weather_data = response.json()
            
            if 'main' not in weather_data:
                logfire.warning(f"Weather API response missing main data for {city}")
                return {"error": f"Incomplete weather data received for {city}"}
            
            # Log successful API call
            logfire.info(f'OpenWeatherMap API call successful for {city}')
            
            # Extract temperature and humidity from API data
            temp = weather_data['main'].get('temp')
            humidity = weather_data['main'].get('humidity')
            description = weather_data.get('weather', [{}])[0].get('description', 'No description available')
            wind_speed = weather_data.get('wind', {}).get('speed', 0)
            country = weather_data.get('sys', {}).get('country', '')
            
            logfire.info(f'Weather data retrieved for {city}: {temp}°C, {humidity}% humidity')
            
            result = {
                "city": city,
                "country": country,
                "temperature": f"{temp} °C",
                "temperature_value": temp,
                "humidity": f"{humidity}%",
                "humidity_value": humidity,
                "description": description,
                "wind_speed": f"{wind_speed} m/s",
                "unit": "metric",
                "timestamp": datetime.datetime.now().isoformat()
            }
            
            # Return only requested data if specified
            if data_type.lower() == "temperature":
                return {
                    "city": city,
                    "temperature": f"{temp} °C",
                    "temperature_value": temp,
                    "unit": "metric"
                }
            elif data_type.lower() == "humidity":
                return {
                    "city": city,
                    "humidity": f"{humidity}%",
                    "humidity_value": humidity
                }
            
            return result
            
        except Exception as e:
            error_msg = f"Error getting weather data: {str(e)}"
            logfire.error(error_msg)
            return {"error": error_msg}

async def main():
    # Example queries that will use tools, Context7, and Sequential Thinking
    queries = [
        # "What's the current time?",
        # "write ASCII-art for 'Joseph Kiran'. use the latest way to do it",
        # "what is the temperature in kerala now? include the date amd time",
        # "which date is 100 days + today?",
        # "How do I use pydantic validators in the latest version? Include a code example.",
        # "How do I use pydantic ai in the latest version to show how to use mcp local server ? Include a code example.",
        # Test sequential thinking with a complex problem
        # "Analyze this step by step: I need to plan a coding project that fetches weather data for 3 cities, calculates the average temperature, and determines the best time to visit based on weather patterns. Break down the approach, identify the tools I'd need, and create a implementation plan.",
        "what is the temperature in kerala and new york now? include the date amd time for both cities",
        ""
    ]

    with logfire.span('fastmcp with tool example'):
        logfire.info('Starting fastmcp with tool example')

        # Run the MCP servers (Context7 and Sequential Thinking)
        async with fastmcp_agent.run_mcp_servers():
            logfire.info('MCP servers running (Context7 and Sequential Thinking enabled)')

            for i, query in enumerate(queries):
                with logfire.span(f'Processing query {i+1}'):
                    logfire.info(f'Query: {query}')

                    result = await fastmcp_agent.run(query)

                    # Access the structured output
                    response = result.output

                    logfire.info(f'Response received for query {i+1}')

                    print("=" * 40)
                    print(f"Answer: {response.answer}")

                    if response.code_snippets:
                        print("\nCode Snippets:")
                        for j, snippet in enumerate(response.code_snippets):
                            print(f"Snippet {j+1}:")
                            print(snippet)

                    if response.references:
                        print("\nReferences:")
                        for ref in response.references:
                            print(f"- {ref}")
                    print("=" * 40)

        logfire.info('fastmcp with tool example completed')

if __name__ == "__main__":
    # Instructions:
    # 1. First, make sure Python 3.13 is installed
    # 2. Create a virtual environment: python -m venv .venv
    # 3. Activate it: .venv\Scripts\activate (Windows) or source .venv/bin/activate (Unix)
    # 4. Install dependencies: uv pip install pydantic-ai-slim[mcp,openai] logfire requests
    # 5. (Optional) Install Deno for Context7 MCP server:
    #    - Windows: winget install DenoLand.Deno
    #    - Or download from https://deno.land/
    # 6. (Optional) Install Docker for Sequential Thinking MCP server:
    #    - Windows: winget install Docker.DockerDesktop
    #    - Or download from https://docker.com/
    #    - Pull the image: docker pull sequentialthinking
    # 7. Run this script: python fastmcp_tool_example.py
    asyncio.run(main()) 
